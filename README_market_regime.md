# 市场状态识别器 (Market Regime Identifier)

基于HMM（隐马尔可夫模型）的市场状态识别工具，用于识别牛市、熊市和震荡市等不同的市场状态。

## 功能特性

- 使用HMM模型识别市场状态
- 基于日收益率和Parkinson波动率作为观测特征
- 支持模型训练和预测功能
- 自动将预测结果合并到refined_a458数据文件中
- 提供命令行接口，使用Fire库

## 依赖项安装

```bash
pip install hmmlearn scikit-learn pandas numpy fire
```

## 使用方法

### 1. 训练模型

```bash
python market_regime_identifier.py train --start_date=20080101
```

参数说明：
- `start_date`: 训练数据的开始日期，格式为YYYYMMDD
- `n_components`: 可选，HMM模型的隐状态数量，默认为3

### 2. 预测市场状态

```bash
python market_regime_identifier.py predict --start_date=20200101
```

参数说明：
- `start_date`: 预测数据的开始日期，格式为YYYYMMDD
- 预测结果会自动合并到`refined_a458_{market}.pkl`文件中

### 3. 自定义隐状态数量

```bash
# 使用4个隐状态（例如：牛市、熊市、震荡市、过渡期）
python market_regime_identifier.py train --start_date=20080101 --n_components=4
```

## 数据要求

### 输入数据
- 市场指数文件：`{market}.csv`，包含以下列：
  - `date`: 日期
  - `open`: 开盘价
  - `close`: 收盘价
  - `high`: 最高价
  - `low`: 最低价
  - `volume`: 成交量

### 输出数据
- 训练后的模型保存在`models/`目录下
- 预测结果会添加`regime_id`列到refined_a458文件中

## 特征工程

模型使用以下两个特征：

1. **日收益率** (`daily_return`)
   - 计算公式：`close.pct_change()`

2. **Parkinson波动率** (`volatility_10d`)
   - 计算公式：`sqrt((1/(4*ln(2))) * E[(ln(H/L))^2])`
   - 使用10日滚动窗口

## 模型说明

- **模型类型**: 高斯隐马尔可夫模型 (Gaussian HMM)
- **协方差类型**: full（完全协方差矩阵）
- **迭代次数**: 100
- **特征标准化**: 使用StandardScaler进行标准化
- **滞后校正**: 预测结果会进行1期滞后，避免look-ahead bias

## 文件结构

```
├── market_regime_identifier.py  # 主程序文件
├── test_market_regime.py       # 测试脚本
├── models/                     # 模型保存目录
│   ├── hmm_regime_model_{market}.pkl  # 训练好的HMM模型
│   └── hmm_scaler_{market}.pkl        # 特征标准化器
└── {market}.csv               # 市场指数数据文件
```

## 注意事项

1. **数据质量**: 确保市场数据文件存在且格式正确
2. **训练数据量**: 建议至少使用50个交易日的数据进行训练
3. **模型保存**: 训练后的模型会自动保存到models目录
4. **环境初始化**: 程序会自动调用`init_qlib()`初始化环境
5. **数据合并**: 预测结果会自动合并到refined_a458文件中，支持MultiIndex格式

## 示例输出

### 训练输出
```
开始训练HMM模型，起始日期: 20080101
训练数据形状: (3000, 2)
模型训练完成，收敛得分: -2.3456
模型已保存到: models/hmm_regime_model_000300.pkl
标准化器已保存到: models/hmm_scaler_000300.pkl
训练完成！
```

### 预测输出
```
开始预测市场状态，起始日期: 20200101
模型已从 models/hmm_regime_model_000300.pkl 加载
预测完成，共 1000 个交易日
状态分布: {0: 300, 1: 400, 2: 300}
regime_id已合并到 refined_a458_000300.pkl，更新后形状: (500000, 460)
预测完成！
```

## 故障排除

1. **ImportError**: 确保安装了所有依赖项
2. **FileNotFoundError**: 检查市场数据文件是否存在
3. **ValueError**: 检查日期格式和数据质量
4. **模型加载失败**: 确保先运行训练步骤

## 扩展功能

可以通过修改代码实现以下扩展：
- 添加更多技术指标作为特征
- 使用不同的HMM变体（如混合高斯HMM）
- 实现在线学习和模型更新
- 添加模型评估和可视化功能