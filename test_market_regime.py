#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试市场状态识别器

依赖项安装:
pip install hmmlearn scikit-learn

使用方法:
python market_regime_identifier.py train --start_date=20080101
python market_regime_identifier.py predict --start_date=20200101
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from market_regime_identifier import MarketRegimeIdentifier
    print("✓ MarketRegimeIdentifier 导入成功")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    print("请确保安装了以下依赖:")
    print("pip install hmmlearn scikit-learn pandas numpy")
    sys.exit(1)

try:
    from configs import config_info
    print(f"✓ 配置加载成功，当前市场: {config_info['market']}")
except ImportError as e:
    print(f"✗ 配置导入失败: {e}")
    sys.exit(1)

print("\n所有导入检查通过！")
print("\n使用示例:")
print("1. 训练模型:")
print("   python market_regime_identifier.py train --start_date=20080101")
print("\n2. 预测状态:")
print("   python market_regime_identifier.py predict --start_date=20200101")
print("\n3. 自定义隐状态数量:")
print("   python market_regime_identifier.py train --start_date=20080101 --n_components=4")