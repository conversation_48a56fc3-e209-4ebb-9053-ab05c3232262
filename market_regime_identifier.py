import datetime
import os
import pickle
from typing import Optional, Tuple

import fire
import numpy as np
import pandas as pd
from hmmlearn import hmm
from sklearn.preprocessing import StandardScaler

from configs import config_info
from util import init_qlib


class MarketRegimeIdentifier:
    """
    市场状态识别器，使用HMM模型识别市场的牛市、熊市和震荡市状态
    """
    
    def __init__(self, n_components: int = 3, random_state: int = 42):
        """
        初始化市场状态识别器
        
        Args:
            n_components: HMM模型的隐状态数量，默认3（牛市、熊市、震荡市）
            random_state: 随机种子
        """
        self.n_components = n_components
        self.random_state = random_state
        self.model = None
        self.scaler = StandardScaler()
        self.feature_columns = ['daily_return', 'volatility_10d']
        
    def _load_market_data(self, start_date: str) -> pd.DataFrame:
        """
        加载市场指数数据
        
        Args:
            start_date: 开始日期，格式为'YYYYMMDD'
            
        Returns:
            市场指数数据DataFrame
        """
        market_file = f"{config_info['market']}.csv"
        if not os.path.exists(market_file):
            raise FileNotFoundError(f"市场数据文件不存在: {market_file}")
            
        df = pd.read_csv(market_file)
        df['date'] = pd.to_datetime(df['date'])
        
        # 过滤开始日期
        start_date_dt = pd.to_datetime(start_date, format='%Y%m%d')
        df = df[df['date'] >= start_date_dt].copy()
        
        return df.sort_values('date').reset_index(drop=True)
    
    def _calculate_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算用于HMM模型的特征
        
        Args:
            df: 市场数据DataFrame
            
        Returns:
            包含特征的DataFrame
        """
        df = df.copy()
        
        # 计算日收益率
        df['daily_return'] = df['close'].pct_change()
        
        # 计算Parkinson波动率（10日）
        df['log_hl_ratio'] = np.log(df['high'] / df['low'])
        df['volatility_10d'] = np.sqrt(
            (1 / (4 * np.log(2))) * 
            df['log_hl_ratio'].pow(2).rolling(window=10).mean()
        )
        
        # 删除包含NaN的行
        df = df.dropna(subset=self.feature_columns)
        
        return df
    
    def train(self, start_date: str, save_model: bool = True) -> None:
        """
        训练HMM模型
        
        Args:
            start_date: 训练数据开始日期，格式为'YYYYMMDD'
            save_model: 是否保存模型到文件
        """
        print(f"开始训练HMM模型，起始日期: {start_date}")
        
        # 加载和处理数据
        df = self._load_market_data(start_date)
        df = self._calculate_features(df)
        
        if len(df) < 50:
            raise ValueError("训练数据太少，至少需要50个交易日的数据")
        
        # 准备观测矩阵
        features = df[self.feature_columns].values
        
        # 标准化特征
        features_scaled = self.scaler.fit_transform(features)
        
        # 训练HMM模型
        self.model = hmm.GaussianHMM(
            n_components=self.n_components,
            covariance_type='full',
            n_iter=100,
            random_state=self.random_state
        )
        
        print(f"训练数据形状: {features_scaled.shape}")
        self.model.fit(features_scaled)
        
        print(f"模型训练完成，收敛得分: {self.model.score(features_scaled):.4f}")
        
        # 保存模型
        if save_model:
            self._save_model()
            
    def _save_model(self) -> None:
        """
        保存训练好的模型和标准化器
        """
        models_dir = 'models'
        os.makedirs(models_dir, exist_ok=True)
        
        model_path = os.path.join(models_dir, f'hmm_regime_model_{config_info["market"]}.pkl')
        scaler_path = os.path.join(models_dir, f'hmm_scaler_{config_info["market"]}.pkl')
        
        with open(model_path, 'wb') as f:
            pickle.dump(self.model, f)
            
        with open(scaler_path, 'wb') as f:
            pickle.dump(self.scaler, f)
            
        print(f"模型已保存到: {model_path}")
        print(f"标准化器已保存到: {scaler_path}")
    
    def _load_model(self) -> bool:
        """
        加载训练好的模型和标准化器
        
        Returns:
            是否成功加载模型
        """
        model_path = os.path.join('models', f'hmm_regime_model_{config_info["market"]}.pkl')
        scaler_path = os.path.join('models', f'hmm_scaler_{config_info["market"]}.pkl')
        
        if not os.path.exists(model_path) or not os.path.exists(scaler_path):
            return False
            
        try:
            with open(model_path, 'rb') as f:
                self.model = pickle.load(f)
                
            with open(scaler_path, 'rb') as f:
                self.scaler = pickle.load(f)
                
            print(f"模型已从 {model_path} 加载")
            return True
        except Exception as e:
            print(f"加载模型失败: {e}")
            return False
    
    def predict(self, start_date: str, merge_to_refined: bool = True) -> pd.DataFrame:
        """
        使用训练好的模型预测市场状态
        
        Args:
            start_date: 预测数据开始日期，格式为'YYYYMMDD'
            merge_to_refined: 是否将结果合并到refined_a458文件中
            
        Returns:
            包含预测结果的DataFrame
        """
        print(f"开始预测市场状态，起始日期: {start_date}")
        
        # 加载模型
        if self.model is None:
            if not self._load_model():
                raise ValueError("未找到训练好的模型，请先运行训练")
        
        # 加载和处理数据
        df = self._load_market_data(start_date)
        df = self._calculate_features(df)
        
        if len(df) == 0:
            raise ValueError("没有可用的预测数据")
        
        # 准备特征
        features = df[self.feature_columns].values
        features_scaled = self.scaler.transform(features)
        
        # 预测状态
        regime_ids = self.model.predict(features_scaled)
        
        # 添加预测结果到DataFrame
        df['regime_id'] = regime_ids
        
        # 滞后校正，避免look-ahead bias
        df['regime_id'] = df['regime_id'].shift(1)
        
        print(f"预测完成，共 {len(df)} 个交易日")
        print(f"状态分布: {pd.Series(regime_ids).value_counts().sort_index().to_dict()}")
        
        # 合并到refined_a458文件
        if merge_to_refined:
            self._merge_to_refined_data(df)
            
        return df[['date', 'regime_id']].dropna()
    
    def _merge_to_refined_data(self, regime_df: pd.DataFrame) -> None:
        """
        将regime预测结果合并到refined_a458文件中
        
        Args:
            regime_df: 包含regime_id的DataFrame
        """
        refined_file = f'refined_a458_{config_info["market"]}.pkl'
        
        if not os.path.exists(refined_file):
            print(f"警告: refined文件不存在: {refined_file}")
            return
            
        try:
            # 读取refined数据
            refined_df = pd.read_pickle(refined_file)
            print(f"原始refined数据形状: {refined_df.shape}")
            
            # 准备合并的regime数据
            regime_merge = regime_df[['date', 'regime_id']].dropna().copy()
            regime_merge.set_index('date', inplace=True)
            
            # 检查refined_df的索引结构
            if isinstance(refined_df.index, pd.MultiIndex):
                # MultiIndex情况，通常是(date, sec_id)
                # 为每个日期的所有股票添加相同的regime_id
                dates_in_refined = refined_df.index.get_level_values(0).unique()
                regime_expanded = []
                
                for date in dates_in_refined:
                    if date in regime_merge.index:
                        regime_id = regime_merge.loc[date, 'regime_id']
                        # 获取该日期的所有股票
                        stocks_on_date = refined_df.loc[date].index
                        for stock in stocks_on_date:
                            regime_expanded.append({
                                'date': date,
                                'sec_id': stock,
                                'regime_id': regime_id
                            })
                
                if regime_expanded:
                    regime_expanded_df = pd.DataFrame(regime_expanded)
                    regime_expanded_df.set_index(['date', 'sec_id'], inplace=True)
                    
                    # 合并数据
                    refined_df = refined_df.merge(
                        regime_expanded_df, 
                        left_index=True, 
                        right_index=True, 
                        how='left'
                    )
            else:
                # 单一索引情况
                refined_df = refined_df.merge(
                    regime_merge, 
                    left_index=True, 
                    right_index=True, 
                    how='left'
                )
            
            # 保存更新后的数据
            refined_df.to_pickle(refined_file)
            print(f"regime_id已合并到 {refined_file}，更新后形状: {refined_df.shape}")
            
            # 显示regime_id的统计信息
            if 'regime_id' in refined_df.columns:
                regime_stats = refined_df['regime_id'].value_counts().sort_index()
                print(f"合并后的regime_id分布: {regime_stats.to_dict()}")
                
        except Exception as e:
            print(f"合并数据时出错: {e}")


def main(action: str, start_date: str = '20080101', n_components: int = 3):
    """
    主函数，使用Fire库启动
    
    Args:
        action: 操作类型，'train' 或 'predict'
        start_date: 开始日期，格式为'YYYYMMDD'
        n_components: HMM模型的隐状态数量
    """
    # 初始化qlib环境
    init_qlib()
    
    # 创建识别器实例
    identifier = MarketRegimeIdentifier(n_components=n_components)
    
    if action == 'train':
        identifier.train(start_date)
        print("训练完成！")
        
    elif action == 'predict':
        result_df = identifier.predict(start_date)
        print("预测完成！")
        print(result_df.head(10))
        
    else:
        print("错误: action参数必须是 'train' 或 'predict'")
        print("使用示例:")
        print("  python market_regime_identifier.py train --start_date=20080101")
        print("  python market_regime_identifier.py predict --start_date=20200101")


if __name__ == '__main__':
    fire.Fire(main)