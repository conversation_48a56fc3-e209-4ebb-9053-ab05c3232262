#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
market_regime_hmm.py
--------------------
工程级 Market‑Regime 识别：特征构造 + HMM 训练 / 推理 + Fire‑CLI
"""

from __future__ import annotations

import logging
from pathlib import Path
from typing import Literal, Optional

import joblib
import numpy as np
import pandas as pd
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler

# --------------------------------------------------------------------- #
#                         日志与常量配置                                #
# --------------------------------------------------------------------- #
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)
log = logging.getLogger("RegimeHMM")

FEATURE_SETS = {
    # 精简：收益 + 波动率
    "basic": [
        "ret_1d",
        "vol_10d",
    ],
    # 完整：趋势 / 动量 / 波动率 / 流动性
    "full": [
        # Trend & Momentum
        "ret_1d",
        "ret_5d",
        "ret_20d",
        "cum_ret_20d",
        "ma20_minus_ma60",
        "ma50_minus_ma200",
        "range_pos_252",
        # Volatility
        "vol_10d",
        "vol_parkinson_10d",
        "vol_gk_10d",
        "atr_14d",
        # Liquidity & Turnover
        "vol_ratio_20d",
        "dollar_vol_ratio_20d",
        # 高阶交互
        "ret_1d_over_vol_10d",
        "ret_5d_over_vol_10d",
        "ma20_slope",
        "ma50_slope",
        "high_low_spread",
    ],
}

# --------------------------------------------------------------------- #
#                              主模型类                                 #
# --------------------------------------------------------------------- #
class MarketRegimeHMM:
    """
    多维特征 HMM 市场状态识别
    ---------------------------------------------------
    Parameters
    ----------
    n_components : 隐状态个数 (Regime 数)
    feature_set  : "basic" | "full" | list[str]
    roll_vol_win : 波动率滚动窗口
    """

    def __init__(
        self,
        n_components: int = 3,
        feature_set: Literal["basic", "full"] | list[str] = "full",
        covariance_type: str = "diag",
        n_iter: int = 200,
        random_state: int = 42,
        tol: float = 1e-4,
        roll_vol_win: int = 10,
    ):
        self.n_components = n_components
        self.covariance_type = covariance_type
        self.n_iter = n_iter
        self.random_state = random_state
        self.tol = tol
        self.roll_vol_win = roll_vol_win

        # 允许自定义特征列
        if isinstance(feature_set, str):
            self.feature_cols = FEATURE_SETS[feature_set]
        else:
            self.feature_cols = feature_set

        # 训练后对象
        self.scaler_: Optional[StandardScaler] = None
        self.hmm_: Optional[GaussianHMM] = None

    # --------------------------- 特征工程 --------------------------- #
    def _build_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        构造含全部候选特征的 DataFrame；最终按 self.feature_cols 选取
        要求 df 有列: open, high, low, close, volume, (可选) free_float_shares
        """
        z = pd.DataFrame(index=df.index)

        # 基础收益
        z["ret_1d"] = df["close"].pct_change()
        z["ret_5d"] = df["close"].pct_change(5)
        z["ret_20d"] = df["close"].pct_change(20)
        z["cum_ret_20d"] = (1 + z["ret_1d"]).rolling(20).apply(np.prod, raw=True) - 1

        # 均线 & 趋势
        ma20 = df["close"].rolling(20).mean()
        ma60 = df["close"].rolling(60).mean()
        ma50 = df["close"].rolling(50).mean()
        ma200 = df["close"].rolling(200).mean()

        z["ma20_minus_ma60"] = ma20 - ma60
        z["ma50_minus_ma200"] = ma50 - ma200
        z["ma20_slope"] = ma20.diff()
        z["ma50_slope"] = ma50.diff()

        # 52 周高低分位 (252 交易日)
        rolling_max = df["close"].rolling(252).max()
        rolling_min = df["close"].rolling(252).min()
        range_diff = rolling_max - rolling_min
        z["range_pos_252"] = np.where(
            range_diff > 0,
            (df["close"] - rolling_min) / range_diff,
            0.5  # 当高低价相等时，设为中位数
        )

        # 波动率 - 标准差
        z["vol_10d"] = z["ret_1d"].rolling(self.roll_vol_win).std()

        # Parkinson 波动率
        c = 1 / (4 * np.log(2))
        z["vol_parkinson_10d"] = (
            c * np.log(df["high"] / df["low"]) ** 2
        ).rolling(self.roll_vol_win).mean() ** 0.5

        # Garman‑Klass 波动率
        log_hl = np.log(df["high"] / df["low"])
        log_co = np.log(df["close"] / df["open"])
        z["vol_gk_10d"] = (
            0.5 * log_hl**2 - (2 * np.log(2) - 1) * log_co**2
        ).rolling(self.roll_vol_win).mean() ** 0.5

        # ATR‑14
        tr1 = (df["high"] - df["low"]).abs()
        tr2 = (df["high"] - df["close"].shift()).abs()
        tr3 = (df["low"] - df["close"].shift()).abs()
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        z["atr_14d"] = tr.rolling(14).mean()

        # 流动性
        z["vol_ratio_20d"] = df["volume"] / df["volume"].rolling(20).mean()
        if "amount" in df.columns:
            z["dollar_vol_ratio_20d"] = df["amount"] / df["amount"].rolling(20).mean()
        else:
            # 近似：收盘价 * 成交量
            dollar_vol = df["close"] * df["volume"]
            z["dollar_vol_ratio_20d"] = dollar_vol / dollar_vol.rolling(20).mean()

        # 高低价差
        z["high_low_spread"] = (df["high"] - df["low"]) / df["close"]

        # 交互项：收益 ‑ 波动率 (防止除零)
        z["ret_1d_over_vol_10d"] = np.where(
            z["vol_10d"] > 0, z["ret_1d"] / z["vol_10d"], 0
        )
        z["ret_5d_over_vol_10d"] = np.where(
            z["vol_10d"] > 0, z["ret_5d"] / z["vol_10d"], 0
        )

        # shift(1) 防止前视
        z = z.shift(1)

        # 选取需要的列并丢 NA
        z = z[self.feature_cols].dropna()

        return z

    # ---------------------------- 训练 ------------------------------ #
    def fit(self, df: pd.DataFrame) -> "MarketRegimeHMM":
        feat = self._build_features(df)
        log.info("Training with %d samples × %d features", *feat.shape)

        self.scaler_ = StandardScaler().fit(feat)
        X = self.scaler_.transform(feat)

        self.hmm_ = GaussianHMM(
            n_components=self.n_components,
            covariance_type=self.covariance_type,
            n_iter=self.n_iter,
            random_state=self.random_state,
            tol=self.tol,
        ).fit(X)

        log.info("HMM converged: %s", self.hmm_.monitor_.converged)
        self._train_index_ = feat.index
        return self

    # ---------------------------- 推理 ------------------------------ #
    def predict(self, df: pd.DataFrame) -> pd.Series:
        if not self.hmm_ or not self.scaler_:
            raise RuntimeError("Model is not fitted or loaded.")

        feat = self._build_features(df)
        X = self.scaler_.transform(feat)
        regimes = self.hmm_.predict(X)

        out = pd.Series(np.nan, index=df.index, name="regime_id", dtype="float64")
        out.loc[feat.index] = regimes
        return out

    # ----------------------------- IO ------------------------------ #
    def save(self, path: str | Path):
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)

        payload = {
            "hmm": self.hmm_,
            "scaler": self.scaler_,
            "meta": {
                "n_components": self.n_components,
                "covariance_type": self.covariance_type,
                "n_iter": self.n_iter,
                "random_state": self.random_state,
                "tol": self.tol,
                "roll_vol_win": self.roll_vol_win,
                "feature_cols": self.feature_cols,
            },
        }
        joblib.dump(payload, path)
        log.info("Model saved to %s", path)

    @classmethod
    def load(cls, path: str | Path) -> "MarketRegimeHMM":
        payload = joblib.load(path)
        meta = payload["meta"]

        self = cls(
            n_components=meta["n_components"],
            feature_set=meta["feature_cols"],
            covariance_type=meta["covariance_type"],
            n_iter=meta["n_iter"],
            random_state=meta["random_state"],
            tol=meta["tol"],
            roll_vol_win=meta["roll_vol_win"],
        )
        self.hmm_ = payload["hmm"]
        self.scaler_ = payload["scaler"]
        return self


# --------------------------------------------------------------------- #
#                              CLI 封装                                 #
# --------------------------------------------------------------------- #
try:
    import fire
except ImportError:  # pragma: no cover
    raise ImportError("Please pip install fire")

class RegimeCLI:
    """基于 python‑fire 的命令行封装"""

    # ------------------------- 工具函数 ---------------------------- #
    @staticmethod
    def _load_market(
        csv_path: str | Path,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ) -> pd.DataFrame:
        df = pd.read_csv(csv_path, parse_dates=["date"]).set_index("date")
        df.sort_index(inplace=True)

        if start_date:
            df = df.loc[start_date:]
        if end_date:
            df = df.loc[:end_date]

        required = {"open", "high", "low", "close", "volume"}
        missing = required - set(df.columns)
        if missing:
            raise ValueError(f"Missing columns in market file: {missing}")
        return df

    # --------------------------- 训练 ------------------------------ #
    def train(
        self,
        market_file: str,
        model_path: str = "models/regime_hmm.pkl",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        n_components: int = 3,
        feature_set: str = "full",
        covariance_type: str = "diag",
        n_iter: int = 200,
        random_state: int = 42,
    ):
        """
        训练市场 Regime HMM
        ------------------
        Parameters
        ----------
        market_file  : 输入 CSV，需含 date, open, high, low, close, volume[, amount]
        model_path   : 输出模型保存路径
        start_date   : 训练区间开始 (YYYY-MM-DD)
        end_date     : 训练区间结束
        n_components : Regime 数
        feature_set  : "basic" | "full" | 自定义逗号分隔字符串
        covariance_type : "diag"/"full"/"spherical"
        """
        if feature_set not in FEATURE_SETS and "," in feature_set:
            feature_set = [f.strip() for f in feature_set.split(",")]

        df = self._load_market(market_file, start_date, end_date)
        model = MarketRegimeHMM(
            n_components=n_components,
            feature_set=feature_set,
            covariance_type=covariance_type,
            n_iter=n_iter,
            random_state=random_state,
        ).fit(df)
        model.save(model_path)

    # --------------------------- 预测 ------------------------------ #
    def predict(
        self,
        market_file: str,
        model_path: str,
        output_file: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        """
        预测区间 Regime ID
        ------------------
        输出 CSV 字段：date, regime_id
        """
        df = self._load_market(market_file, start_date, end_date)
        model = MarketRegimeHMM.load(model_path)
        regimes = model.predict(df)

        res = regimes.dropna().astype(int).to_frame()
        if output_file:
            Path(output_file).parent.mkdir(parents=True, exist_ok=True)
            res.to_csv(output_file, index_label="date")
            log.info("Prediction written to %s", output_file)
        else:
            print(res.tail(15))


# ------------------------------ main --------------------------------- #
if __name__ == "__main__":
    fire.Fire(RegimeCLI)
