#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
regime_cli.py  ·  CLI for training and predicting market regimes with HMM
"""

import os
import joblib
import numpy as np
import pandas as pd
from pathlib import Path
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import fire


class MarketRegimeHMM:
    """
    A tiny wrapper around hmmlearn.GaussianHMM that
    * builds daily return & rolling volatility features
    * standardises them
    * trains / predicts regime ID
    """

    def __init__(
        self,
        n_components: int = 2,
        covariance_type: str = "full",
        n_iter: int = 100,
        tol: float = 1e-4,
        random_state: int | None = 42,
        roll_vol_window: int = 10,
    ):
        self.n_components = n_components
        self.covariance_type = covariance_type
        self.n_iter = n_iter
        self.tol = tol
        self.random_state = random_state
        self.roll_vol_window = roll_vol_window

        self.scaler_: StandardScaler | None = None
        self.hmm_: GaussianHMM | None = None

    # ------------------------------------------------------------------ #
    #                        internal utilities                          #
    # ------------------------------------------------------------------ #
    def _build_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Parameters
        ----------
        df : DataFrame with columns ['close', 'high', 'low']
              index must be datetime‑like and sorted ascending.
        Returns
        -------
        DataFrame with ['ret', 'vol'] (NaN rows dropped)
        """
        tmp = df.copy()
        tmp["ret"] = tmp["close"].pct_change()

        # 简化：用收盘收益率 10 日标准差作为波动率；也可替换成 Parkinson/G‑K 等
        tmp["vol"] = tmp["ret"].rolling(self.roll_vol_window).std()

        tmp = tmp[["ret", "vol"]].dropna()
        return tmp

    # ------------------------------------------------------------------ #
    #                             public API                             #
    # ------------------------------------------------------------------ #
    def fit(self, df: pd.DataFrame) -> "MarketRegimeHMM":
        """
        Train HMM on given price series (single index).

        df 必须至少含 'close'、'high'、'low' 三列；
        行索引必须是日期，并已排序。
        """
        feat = self._build_features(df)

        # 标准化
        self.scaler_ = StandardScaler().fit(feat)
        X = self.scaler_.transform(feat)

        # HMM
        self.hmm_ = GaussianHMM(
            n_components=self.n_components,
            covariance_type=self.covariance_type,
            n_iter=self.n_iter,
            tol=self.tol,
            random_state=self.random_state,
        ).fit(X)

        self._index_ = feat.index  # 仅用于调试
        return self

    def predict(self, df: pd.DataFrame) -> pd.Series:
        """
        返回与输入 df 行数一致的 pd.Series (regime_id, int)
        未能计算特征的日期（NaN 行）以 NaN 返回
        """
        if self.hmm_ is None or self.scaler_ is None:
            raise RuntimeError("Model is not fitted. Call `fit` or `load` first.")

        feat = self._build_features(df)
        X = self.scaler_.transform(feat)
        regimes = pd.Series(self.hmm_.predict(X), index=feat.index, name="regime_id")

        # 把缺失日期补齐
        out = pd.Series(index=df.index, dtype="float64")
        out.loc[regimes.index] = regimes
        return out

    # ----------------------------- I/O ----------------------------- #
    def save(self, path: str | Path):
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)
        joblib.dump(
            {
                "model": self.hmm_,
                "scaler": self.scaler_,
                "meta": {
                    "n_components": self.n_components,
                    "covariance_type": self.covariance_type,
                    "roll_vol_window": self.roll_vol_window,
                },
            },
            path,
        )

    @classmethod
    def load(cls, path: str | Path) -> "MarketRegimeHMM":
        obj = joblib.load(path)
        meta = obj["meta"]
        self = cls(
            n_components=meta["n_components"],
            covariance_type=meta["covariance_type"],
            roll_vol_window=meta["roll_vol_window"],
        )
        self.hmm_ = obj["model"]
        self.scaler_ = obj["scaler"]
        return self


# ====================================================================== #
#                              CLI Wrapper                               #
# ====================================================================== #
class RegimeCLI:
    """
    fire‑based command‑line interface.

    Example:
    --------
    # 训练
    python regime_cli.py train                        \\
        --market_file data/index.csv                  \\
        --start_date 2020-01-01 --end_date 2024-12-31 \\
        --model_path models/sh_regime.pkl --n_components 3

    # 预测
    python regime_cli.py predict                       \\
        --market_file data/index.csv                   \\
        --start_date 2025-01-01 --end_date 2025-07-31  \\
        --model_path models/sh_regime.pkl              \\
        --output_file data/regime_pred.csv
    """

    # -------------------------- helpers -------------------------- #
    @staticmethod
    def _load_market(
        path: str | Path, start_date: str | None, end_date: str | None
    ) -> pd.DataFrame:
        df = pd.read_csv(path, parse_dates=["date"]).set_index("date")
        df.sort_index(inplace=True)

        if start_date:
            df = df.loc[start_date:]
        if end_date:
            df = df.loc[:end_date]

        required = {"close", "high", "low"}
        if not required <= set(df.columns):
            raise ValueError(f"market_file must contain columns {required!r}")
        return df

    # ------------------------ main commands ----------------------- #
    def train(
        self,
        market_file: str,
        start_date: str | None = None,
        end_date: str | None = None,
        model_path: str = "models/regime_hmm.pkl",
        n_components: int = 2,
        covariance_type: str = "full",
        n_iter: int = 100,
        roll_vol_window: int = 10,
    ):
        """
        训练 HMM 并将模型保存到 `model_path`.
        """
        df = self._load_market(market_file, start_date, end_date)
        mr = MarketRegimeHMM(
            n_components=n_components,
            covariance_type=covariance_type,
            n_iter=n_iter,
            roll_vol_window=roll_vol_window,
        ).fit(df)
        mr.save(model_path)
        print(f"✔ Model trained on {len(df)} rows; saved to {model_path}")

    def predict(
        self,
        market_file: str,
        start_date: str | None = None,
        end_date: str | None = None,
        model_path: str = "models/regime_hmm.pkl",
        output_file: str | None = None,
    ):
        """
        使用已保存模型，预测每日 regime ID。
        结果打印至 stdout，并可选保存到 CSV.
        """
        df = self._load_market(market_file, start_date, end_date)
        mr = MarketRegimeHMM.load(model_path)
        regimes = mr.predict(df)
        df_out = df.assign(regime_id=regimes)

        if output_file:
            Path(output_file).parent.mkdir(parents=True, exist_ok=True)
            df_out.to_csv(output_file, index=True)
            print(f"✔ Prediction saved to {output_file}")
        else:
            print(df_out[["close", "regime_id"]].tail(15))


# ---------------------------------------------------------------------- #
if __name__ == "__main__":
    fire.Fire(RegimeCLI)
